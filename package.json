{"name": "borders-electrical-services", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@fontsource/fira-mono": "^5.0.0", "@neoconfetti/svelte": "^2.0.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/postcss": "^4.1.11", "autoprefixer": "^10.4.21", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "postcss": "^8.5.6", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.25.0", "svelte-check": "^4.0.0", "svelte-preprocess": "^6.0.3", "tailwindcss": "^4.1.11", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^7.0.4"}, "dependencies": {"@skeletonlabs/skeleton": "^3.1.7", "@skeletonlabs/tw-plugin": "^0.4.1"}}